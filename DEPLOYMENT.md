# 🚀 DeerFlow 一键部署指南

本文档提供 DeerFlow 项目的完整部署指南，支持 Docker Compose 一键启动。

## 📋 部署前准备

### 系统要求

- **操作系统**: Linux/macOS/Windows
- **Docker**: 版本 20.10+ 
- **Docker Compose**: 版本 2.0+
- **内存**: 建议 4GB+
- **存储**: 建议 10GB+ 可用空间

### 检查环境

```bash
# 检查 Docker 版本
docker --version

# 检查 Docker Compose 版本  
docker compose version

# 确保 Docker 服务运行中
docker info
```

## 🔧 快速部署

### 1. 获取项目代码

```bash
# 克隆项目
git clone https://github.com/bytedance/deer-flow.git
cd deer-flow
```

### 2. 配置环境变量

复制并编辑环境配置文件：

```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
nano .env  # 或使用其他编辑器
```

### 3. 必要配置项

在 `.env` 文件中，**必须配置**以下项目：

```bash
# ===========================================
# 🔑 LLM API 配置 (必填)
# ===========================================
BASIC_MODEL__base_url=https://api.siliconflow.cn/v1
BASIC_MODEL__model=Qwen/Qwen3-8B
BASIC_MODEL__api_key=你的SiliconFlow_API_密钥

# ===========================================
# 🌐 网络配置 (推荐保持默认)
# ===========================================
NEXT_PUBLIC_API_URL=http://backend:8000/api

# ===========================================
# 🔍 搜索引擎配置 (可选，默认 DuckDuckGo)
# ===========================================
SEARCH_API=duckduckgo

# ===========================================
# 🐳 应用配置
# ===========================================
DEBUG=False
APP_ENV=production
```

### 4. 一键启动

```bash
# 构建并启动所有服务
docker compose up -d

# 查看启动状态
docker compose ps

# 查看日志 (可选)
docker compose logs -f
```

### 5. 访问应用

部署成功后，通过以下地址访问：

- **主应用**: http://你的服务器IP:3366
- **Landing 页面**: http://你的服务器IP:3366/landing

## 📊 验证部署

### 检查服务状态

```bash
# 查看容器运行状态
docker compose ps

# 预期输出示例:
# NAME                   IMAGE              COMMAND                  SERVICE    CREATED         STATUS         PORTS
# deer-flow-backend      deer-flow_backend  "uv run python serv…"   backend    2 minutes ago   Up 2 minutes   8000/tcp
# deer-flow-frontend     deer-flow_frontend "docker-entrypoint.s…"  frontend   2 minutes ago   Up 2 minutes   0.0.0.0:3366->3000/tcp
```

### 健康检查

```bash
# 检查后端 API
curl http://localhost:3366/api/config

# 预期返回 JSON 配置信息，包含:
# {"rag":{"provider":null},"models":{"basic":["Qwen/Qwen3-8B"]}}
```

### 功能测试

1. 打开浏览器访问 `http://你的服务器IP:3366`
2. 应该自动跳转到深度研究页面
3. 尝试发起一个简单的研究请求
4. 确认搜索和 AI 回复功能正常

## 🛠️ 高级配置

### 自定义端口

如需修改外部访问端口，编辑 `docker-compose.yml`：

```yaml
frontend:
  ports:
    - "你的端口:3000"  # 例如 "8080:3000"
```

### 配置其他搜索引擎

在 `.env` 中修改搜索引擎：

```bash
# 使用 Tavily (需要 API Key)
SEARCH_API=tavily
TAVILY_API_KEY=你的Tavily密钥

# 使用 Brave Search (需要 API Key)  
SEARCH_API=brave_search
BRAVE_SEARCH_API_KEY=你的Brave密钥

# 使用 DuckDuckGo (无需 API Key，默认)
SEARCH_API=duckduckgo
```

### 配置其他 LLM 提供商

```bash
# 使用 DeepSeek
BASIC_MODEL__base_url=https://api.deepseek.com
BASIC_MODEL__model=deepseek-chat
BASIC_MODEL__api_key=你的DeepSeek密钥

# 使用 OpenAI
BASIC_MODEL__base_url=https://api.openai.com/v1
BASIC_MODEL__model=gpt-4
BASIC_MODEL__api_key=你的OpenAI密钥
```

## 🔄 管理操作

### 重启服务

```bash
# 重启所有服务
docker compose restart

# 重启特定服务
docker compose restart frontend
docker compose restart backend
```

### 更新部署

```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker compose down
docker compose build --no-cache
docker compose up -d
```

### 查看日志

```bash
# 查看所有服务日志
docker compose logs

# 实时查看日志
docker compose logs -f

# 查看特定服务日志
docker compose logs frontend
docker compose logs backend
```

### 停止服务

```bash
# 停止所有服务
docker compose down

# 停止并删除数据卷 (谨慎使用)
docker compose down -v
```

## 🚨 故障排除

### 常见问题

**1. 端口被占用**
```bash
# 检查端口占用
netstat -tlnp | grep 3366

# 修改 docker-compose.yml 中的端口映射
```

**2. API 连接失败**
```bash
# 检查 .env 中的 NEXT_PUBLIC_API_URL 配置
# 确保为: http://backend:8000/api

# 检查后端容器状态
docker compose logs backend
```

**3. LLM API 调用失败**
```bash
# 检查 API Key 配置
# 验证 SiliconFlow API Key 有效性
# 确认网络可访问 api.siliconflow.cn
```

**4. 前端构建失败**
```bash
# 清理并重新构建
docker compose down
docker system prune -f
docker compose build --no-cache frontend
docker compose up -d
```

### 获取帮助

- **项目文档**: [配置指南](docs/configuration_guide.md)
- **GitHub Issues**: https://github.com/bytedance/deer-flow/issues
- **社区讨论**: 查看项目 README 中的社区链接

## 📝 生产环境建议

1. **安全性**:
   - 使用环境变量或密钥管理服务存储 API Key
   - 配置防火墙规则，仅开放必要端口
   - 定期更新 Docker 镜像

2. **性能优化**:
   - 配置适当的资源限制
   - 使用 nginx 作为反向代理
   - 启用日志轮转

3. **监控**:
   - 配置健康检查
   - 设置日志收集
   - 监控资源使用情况

4. **备份**:
   - 定期备份配置文件
   - 备份重要数据卷
   - 测试恢复流程
