# 🔧 Docker 构建问题修复指南

## 问题诊断

您遇到的错误是前端构建失败，主要原因是：
1. 环境变量传递问题
2. 前端缺少必要的 .env 文件
3. Docker 构建参数配置问题

## 已修复的问题

我已经修复了以下文件：

### 1. `web/Dockerfile` - 修复环境变量传递
```dockerfile
# 修复前
ARG NEXT_PUBLIC_API_URL

# 修复后  
ARG NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
```

### 2. `docker-compose.yml` - 修复构建参数
```yaml
# 修复前
args:
  - NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL

# 修复后
args:
  - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
```

### 3. `web/.env` - 新增前端环境文件
```bash
NEXT_PUBLIC_API_URL=http://backend:8000/api
SKIP_ENV_VALIDATION=1
```

## 现在重新部署

### 清理之前的构建缓存
```bash
# 停止并删除容器
docker compose down

# 清理构建缓存
docker system prune -f

# 删除相关镜像（可选）
docker rmi deer-flow-main_frontend deer-flow-main_backend 2>/dev/null || true
```

### 重新构建和启动
```bash
# 重新构建（不使用缓存）
docker compose build --no-cache

# 启动服务
docker compose up -d

# 查看启动状态
docker compose ps
```

### 如果还有问题，查看详细日志
```bash
# 查看前端构建日志
docker compose logs frontend

# 查看后端日志
docker compose logs backend

# 实时查看所有日志
docker compose logs -f
```

## 验证部署成功

### 1. 检查容器状态
```bash
docker compose ps
```
应该看到两个容器都是 "Up" 状态。

### 2. 测试 API 连接
```bash
curl http://localhost:3366/api/config
```
应该返回包含 Qwen 模型配置的 JSON。

### 3. 访问前端
打开浏览器访问: http://localhost:3366
应该自动跳转到深度研究页面。

## 如果仍然失败

### 方案 A: 分步构建
```bash
# 先只构建后端
docker compose build backend
docker compose up -d backend

# 等后端启动后再构建前端
docker compose build frontend
docker compose up -d frontend
```

### 方案 B: 检查具体错误
```bash
# 单独构建前端查看详细错误
docker build -t test-frontend \
  --build-arg NEXT_PUBLIC_API_URL=http://backend:8000/api \
  ./web
```

### 方案 C: 使用开发模式
如果 Docker 构建仍有问题，可以临时使用本地开发模式：
```bash
# 后端
uv run python server.py

# 前端（新终端）
cd web && pnpm dev
```

## 常见错误解决

### 错误: "Lockfile not found"
- 确保 `web/pnpm-lock.yaml` 文件存在
- 重新生成: `cd web && pnpm install`

### 错误: "Environment validation failed"
- 确保 `web/.env` 文件存在且包含必要变量
- 设置 `SKIP_ENV_VALIDATION=1`

### 错误: "Network deer-flow-network not found"
```bash
docker network create deer-flow-network
```

现在请尝试重新部署！
