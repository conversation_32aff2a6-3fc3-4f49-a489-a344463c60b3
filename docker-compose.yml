services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: deer-flow-backend
    # Remove the ports section to not expose to host
    env_file:
      - .env
    volumes:
      - ./conf.yaml:/app/conf.yaml:ro
    restart: unless-stopped
    networks:
      - deer-flow-network

  frontend:
    build:
      context: ./web
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
    container_name: deer-flow-frontend
    ports:
      - "3366:3000"
    env_file:
      - .env
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - deer-flow-network

networks:
  deer-flow-network:
    driver: bridge
